import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Confetti from "react-confetti";
import { Gallery, Item } from "react-photoswipe-gallery";
import { useInView } from "react-intersection-observer";
import "photoswipe/dist/photoswipe.css";
import "../styles/BirthdayMain.css";

// Random Explosions Component
// Ghibli-style Firework Explosions Component
function GhibliFireworks() {
	const [fireworks, setFireworks] = useState([]);

	useEffect(() => {
		const createFirework = () => {
			const id = Date.now() + Math.random();
			const x = Math.random() * (window.innerWidth - 200) + 100;
			const y = Math.random() * (window.innerHeight - 300) + 100;
			const colors = ["#ff69b4", "#87ceeb", "#98fb98", "#ffd700", "#dda0dd", "#f0e68c"];
			const color = colors[Math.floor(Math.random() * colors.length)];
			const size = Math.random() * 0.5 + 0.8; // 0.8 to 1.3

			const newFirework = { id, x, y, color, size };
			setFireworks((prev) => [...prev, newFirework]);

			// Remove firework after animation
			setTimeout(() => {
				setFireworks((prev) => prev.filter((fw) => fw.id !== id));
			}, 4000);
		};

		// Create firework every 5 seconds
		const interval = setInterval(createFirework, 5000);

		return () => clearInterval(interval);
	}, []);

	return (
		<div className="ghibli-fireworks">
			<AnimatePresence>
				{fireworks.map((firework) => (
					<motion.div
						key={firework.id}
						className="firework-container"
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						style={{
							position: "fixed",
							left: firework.x,
							top: firework.y,
							zIndex: 1,
							pointerEvents: "none",
						}}>
						{/* Main firework burst */}
						<motion.div
							className="firework-burst"
							initial={{ scale: 0 }}
							animate={{ scale: firework.size }}
							transition={{ duration: 0.3, ease: "easeOut" }}>
							{/* Primary burst particles */}
							{[...Array(16)].map((_, i) => {
								const angle = (i * 22.5 * Math.PI) / 180;
								const distance = 80 + Math.random() * 40;
								return (
									<motion.div
										key={`primary-${i}`}
										className="firework-particle"
										initial={{
											x: 0,
											y: 0,
											scale: 0,
											opacity: 0,
										}}
										animate={{
											x: Math.cos(angle) * distance,
											y: Math.sin(angle) * distance,
											scale: [0, 1.2, 0.8, 0],
											opacity: [0, 1, 0.8, 0],
										}}
										transition={{
											duration: 2.5,
											delay: 0.1,
											ease: "easeOut",
										}}
										style={{
											position: "absolute",
											width: "6px",
											height: "6px",
											borderRadius: "50%",
											backgroundColor: firework.color,
											boxShadow: `0 0 10px ${firework.color}`,
										}}
									/>
								);
							})}

							{/* Secondary sparkle particles */}
							{[...Array(24)].map((_, i) => {
								const angle = (i * 15 * Math.PI) / 180;
								const distance = 60 + Math.random() * 60;
								return (
									<motion.div
										key={`secondary-${i}`}
										className="firework-sparkle"
										initial={{
											x: 0,
											y: 0,
											scale: 0,
											opacity: 0,
										}}
										animate={{
											x: Math.cos(angle) * distance,
											y: Math.sin(angle) * distance + Math.random() * 20 - 10,
											scale: [0, 0.8, 0.4, 0],
											opacity: [0, 0.9, 0.6, 0],
											rotate: [0, 180, 360],
										}}
										transition={{
											duration: 3,
											delay: 0.2 + Math.random() * 0.3,
											ease: "easeOut",
										}}
										style={{
											position: "absolute",
											fontSize: "8px",
											color: firework.color,
											filter: `drop-shadow(0 0 3px ${firework.color})`,
										}}>
										✨
									</motion.div>
								);
							})}

							{/* Trailing sparkles that fall down */}
							{[...Array(12)].map((_, i) => {
								const angle = (i * 30 * Math.PI) / 180;
								const distance = 40 + Math.random() * 30;
								return (
									<motion.div
										key={`trail-${i}`}
										className="firework-trail"
										initial={{
											x: Math.cos(angle) * distance,
											y: Math.sin(angle) * distance,
											scale: 0,
											opacity: 0,
										}}
										animate={{
											x: Math.cos(angle) * distance + (Math.random() - 0.5) * 40,
											y: Math.sin(angle) * distance + 100 + Math.random() * 50,
											scale: [0, 0.6, 0.3, 0],
											opacity: [0, 0.8, 0.4, 0],
										}}
										transition={{
											duration: 2.8,
											delay: 0.5 + Math.random() * 0.5,
											ease: "easeIn",
										}}
										style={{
											position: "absolute",
											width: "3px",
											height: "3px",
											borderRadius: "50%",
											backgroundColor: firework.color,
											boxShadow: `0 0 6px ${firework.color}`,
										}}
									/>
								);
							})}

							{/* Central glow */}
							<motion.div
								className="firework-glow"
								initial={{ scale: 0, opacity: 0 }}
								animate={{
									scale: [0, 2, 1.5, 0],
									opacity: [0, 0.8, 0.4, 0],
								}}
								transition={{
									duration: 2,
									ease: "easeOut",
								}}
								style={{
									position: "absolute",
									width: "20px",
									height: "20px",
									borderRadius: "50%",
									backgroundColor: firework.color,
									boxShadow: `0 0 30px ${firework.color}`,
									left: "-10px",
									top: "-10px",
								}}
							/>
						</motion.div>
					</motion.div>
				))}
			</AnimatePresence>
		</div>
	);
}

function BirthdayMain() {
	const [showConfetti, setShowConfetti] = useState(true);
	const [windowSize, setWindowSize] = useState({
		width: window.innerWidth,
		height: window.innerHeight,
	});
	const [selectedMemory, setSelectedMemory] = useState(null);
	const [currentReasonIndex, setCurrentReasonIndex] = useState(0);
	const [showReasonsModal, setShowReasonsModal] = useState(false);
	const [selectedReason, setSelectedReason] = useState(null);
	const [surpriseRevealed, setSurpriseRevealed] = useState(false);
	const [showDrawingExplosion, setShowDrawingExplosion] = useState(false);
	const [drawingSettled, setDrawingSettled] = useState(false);
	const [counterValue, setCounterValue] = useState(0);
	const [counterFinished, setCounterFinished] = useState(false);
	const [showVideoExplosion, setShowVideoExplosion] = useState(false);
	const [videoSettled, setVideoSettled] = useState(false);
	const [videoRevealed, setVideoRevealed] = useState(false);
	const videoRef = useRef(null);

	// Scratch card states
	const [scratchProgress, setScratchProgress] = useState(0);
	const [showFakeImage, setShowFakeImage] = useState(false);
	const [showJokingMessage, setShowJokingMessage] = useState(false);
	const [showRealImage, setShowRealImage] = useState(false);
	const [isScratching, setIsScratching] = useState(false);
	const scratchCanvasRef = useRef(null);
	const [scratchStarted, setScratchStarted] = useState(false);
	const [scratchCardScale, setScratchCardScale] = useState(0.6);

	// Scroll blocking states
	const [allowScrollPastMessage, setAllowScrollPastMessage] = useState(false);
	const [allowScrollPastReasons, setAllowScrollPastReasons] = useState(false);

	const [headerRef, headerInView] = useInView({ triggerOnce: true });
	const [messageRef, messageInView] = useInView({ triggerOnce: true });
	const [counterRef, counterInView] = useInView({ triggerOnce: true });
	const [memoriesRef, memoriesInView] = useInView({ triggerOnce: true });
	const [reasonsRef, reasonsInView] = useInView({ triggerOnce: true });
	const [moodsRef, moodsInView] = useInView({ triggerOnce: true });
	const [scratchRef, scratchInView] = useInView({ triggerOnce: true });

	useEffect(() => {
		const handleResize = () => {
			setWindowSize({
				width: window.innerWidth,
				height: window.innerHeight,
			});
		};

		window.addEventListener("resize", handleResize);

		const timer = setTimeout(() => setShowConfetti(false), 15000);

		return () => {
			window.removeEventListener("resize", handleResize);
			clearTimeout(timer);
		};
	}, []);

	// Trigger surprise when reasons section comes into view
	useEffect(() => {
		if (reasonsInView && !surpriseRevealed) {
			// Add a small delay to make it more dramatic
			const timer = setTimeout(() => {
				setSurpriseRevealed(true);
				// Start drawing explosion after title animation
				setTimeout(() => {
					setShowDrawingExplosion(true);
					// Settle drawing after explosion
					setTimeout(() => {
						setDrawingSettled(true);
						// Allow scrolling past reasons section when drawing is settled
						setAllowScrollPastReasons(true);
					}, 2000);
				}, 1500);
			}, 1500);
			return () => clearTimeout(timer);
		}
	}, [reasonsInView, surpriseRevealed]);

	// Counter animation effect - only start after video is settled
	useEffect(() => {
		if (counterInView && videoSettled && !counterFinished) {
			// Calculate days since June 21, 2024
			const startDate = new Date("2024-06-21");
			const currentDate = new Date();
			const timeDifference = currentDate.getTime() - startDate.getTime();
			const totalDays = Math.floor(timeDifference / (1000 * 3600 * 24));

			let currentCount = 0;
			let animationSpeed = 300; // Start slower (300ms between increments)

			const animateCounter = () => {
				if (currentCount < totalDays) {
					setCounterValue(currentCount);
					currentCount++;

					// Speed up dramatically after first 15 counts
					if (currentCount <= 15) {
						// First 15 counts: slow (300ms each)
						animationSpeed = 300;
					} else {
						// After 15 counts: calculate speed to finish in 3-4 seconds
						const remainingCounts = totalDays - currentCount;
						const targetDuration = 3500; // 3.5 seconds to finish
						animationSpeed = Math.max(5, targetDuration / remainingCounts);
					}

					setTimeout(animateCounter, animationSpeed);
				} else {
					setCounterValue(totalDays);
					setCounterFinished(true);
				}
			};

			// Start animation after a small delay
			const timer = setTimeout(animateCounter, 500);
			return () => clearTimeout(timer);
		}
	}, [counterInView, videoSettled, counterFinished]);

	// Video reveal effect when message section comes into view
	useEffect(() => {
		if (messageInView && !videoRevealed) {
			// Add a small delay to make it more dramatic
			const timer = setTimeout(() => {
				setVideoRevealed(true);
				// Start video explosion immediately
				setTimeout(() => {
					setShowVideoExplosion(true);
				}, 500);
			}, 2000);
			return () => clearTimeout(timer);
		}
	}, [messageInView, videoRevealed]);

	// Allow scrolling past message section when video is settled
	useEffect(() => {
		if (videoSettled) {
			setAllowScrollPastMessage(true);
		}
	}, [videoSettled]);

	// Handle video end or escape key
	useEffect(() => {
		const handleKeyPress = (event) => {
			if (event.key === "Escape" && showVideoExplosion) {
				setShowVideoExplosion(false);
				setVideoSettled(true);
			}
		};

		const handleVideoEnd = () => {
			if (showVideoExplosion) {
				setShowVideoExplosion(false);
				setVideoSettled(true);
			}
		};

		if (showVideoExplosion) {
			window.addEventListener("keydown", handleKeyPress);
			// Add event listener to video element when it's available
			const videoElement = document.querySelector(".video-explosion");
			if (videoElement) {
				videoElement.addEventListener("ended", handleVideoEnd);
			}
		}

		return () => {
			window.removeEventListener("keydown", handleKeyPress);
			const videoElement = document.querySelector(".video-explosion");
			if (videoElement) {
				videoElement.removeEventListener("ended", handleVideoEnd);
			}
		};
	}, [showVideoExplosion]);

	// Auto-play video when explosion starts
	useEffect(() => {
		if (showVideoExplosion && videoRef.current) {
			// Small delay to ensure video element is rendered
			const timer = setTimeout(() => {
				if (videoRef.current) {
					// Start muted to comply with browser autoplay policies
					videoRef.current.muted = true;
					videoRef.current
						.play()
						.then(() => {
							console.log("Video started playing (muted)");
							// After a short delay, try to unmute (user can also use controls)
							setTimeout(() => {
								if (videoRef.current) {
									videoRef.current.muted = false;
								}
							}, 1000);
						})
						.catch((error) => {
							console.log("Video autoplay failed:", error);
							// Fallback: video will still have controls for manual play
						});
				}
			}, 500);
			return () => clearTimeout(timer);
		}
	}, [showVideoExplosion]);

	// Scroll blocking effect
	useEffect(() => {
		const handleScroll = (e) => {
			const messageSection = document.querySelector(".message-section");
			const reasonsSection = document.querySelector(".reasons-section");

			if (messageSection && !allowScrollPastMessage) {
				const messageSectionBottom = messageSection.offsetTop + messageSection.offsetHeight;
				if (window.scrollY > messageSectionBottom - window.innerHeight / 2) {
					e.preventDefault();
					window.scrollTo(0, messageSectionBottom - window.innerHeight / 2);
				}
			}

			if (reasonsSection && !allowScrollPastReasons) {
				const reasonsSectionBottom = reasonsSection.offsetTop + reasonsSection.offsetHeight;
				if (window.scrollY > reasonsSectionBottom - window.innerHeight / 2) {
					e.preventDefault();
					window.scrollTo(0, reasonsSectionBottom - window.innerHeight / 2);
				}
			}
		};

		window.addEventListener("scroll", handleScroll, { passive: false });
		return () => window.removeEventListener("scroll", handleScroll);
	}, [allowScrollPastMessage, allowScrollPastReasons]);

	// Scratch card size animation when fake image is revealed
	useEffect(() => {
		if (showFakeImage) {
			// Scale to full size as soon as fake image is revealed
			setTimeout(() => {
				setScratchCardScale(1);
			}, 500);
		}
	}, [showFakeImage]);

	// Initialize scratch canvas
	useEffect(() => {
		if (scratchInView && scratchCanvasRef.current) {
			const canvas = scratchCanvasRef.current;
			const ctx = canvas.getContext("2d");

			// Create a beautiful radial gradient background
			const centerX = canvas.width / 2;
			const centerY = canvas.height / 2;
			const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, Math.max(canvas.width, canvas.height) / 2);
			gradient.addColorStop(0, "#ff69b4");
			gradient.addColorStop(0.3, "#ff1493");
			gradient.addColorStop(0.7, "#ec4899");
			gradient.addColorStop(1, "#be185d");
			ctx.fillStyle = gradient;
			ctx.fillRect(0, 0, canvas.width, canvas.height);

			// Create cute stick figure drawing
			ctx.strokeStyle = "#fff";
			ctx.fillStyle = "#fff";
			ctx.lineWidth = 4;
			ctx.lineCap = "round";

			// Draw two stick figures holding hands
			const figure1X = centerX - 80;
			const figure2X = centerX + 80;
			const figureY = centerY;

			// Figure 1 (left)
			// Head
			ctx.beginPath();
			ctx.arc(figure1X, figureY - 60, 20, 0, 2 * Math.PI);
			ctx.stroke();
			// Body
			ctx.beginPath();
			ctx.moveTo(figure1X, figureY - 40);
			ctx.lineTo(figure1X, figureY + 20);
			ctx.stroke();
			// Arms
			ctx.beginPath();
			ctx.moveTo(figure1X, figureY - 20);
			ctx.lineTo(figure1X - 25, figureY - 5);
			ctx.stroke();
			ctx.beginPath();
			ctx.moveTo(figure1X, figureY - 20);
			ctx.lineTo(figure1X + 25, figureY - 5);
			ctx.stroke();
			// Legs
			ctx.beginPath();
			ctx.moveTo(figure1X, figureY + 20);
			ctx.lineTo(figure1X - 20, figureY + 50);
			ctx.stroke();
			ctx.beginPath();
			ctx.moveTo(figure1X, figureY + 20);
			ctx.lineTo(figure1X + 20, figureY + 50);
			ctx.stroke();

			// Figure 2 (right) - Girl
			// Head
			ctx.beginPath();
			ctx.arc(figure2X, figureY - 60, 20, 0, 2 * Math.PI);
			ctx.stroke();

			// Long hair (flowing on both sides)
			ctx.beginPath();
			// Left side hair
			ctx.moveTo(figure2X - 15, figureY - 70);
			ctx.quadraticCurveTo(figure2X - 30, figureY - 55, figure2X - 25, figureY - 35);
			ctx.stroke();
			// Right side hair
			ctx.beginPath();
			ctx.moveTo(figure2X + 15, figureY - 70);
			ctx.quadraticCurveTo(figure2X + 30, figureY - 55, figure2X + 25, figureY - 35);
			ctx.stroke();
			// Hair on top
			ctx.beginPath();
			ctx.moveTo(figure2X - 10, figureY - 78);
			ctx.quadraticCurveTo(figure2X, figureY - 85, figure2X + 10, figureY - 78);
			ctx.stroke();

			// Body (shorter for dress)
			ctx.beginPath();
			ctx.moveTo(figure2X, figureY - 40);
			ctx.lineTo(figure2X, figureY + 5);
			ctx.stroke();

			// Dress/skirt (triangle shape)
			ctx.beginPath();
			ctx.moveTo(figure2X, figureY + 5);
			ctx.lineTo(figure2X - 25, figureY + 25);
			ctx.lineTo(figure2X + 25, figureY + 25);
			ctx.lineTo(figure2X, figureY + 5);
			ctx.stroke();

			// Arms
			ctx.beginPath();
			ctx.moveTo(figure2X, figureY - 20);
			ctx.lineTo(figure2X - 25, figureY - 5);
			ctx.stroke();
			ctx.beginPath();
			ctx.moveTo(figure2X, figureY - 20);
			ctx.lineTo(figure2X + 25, figureY - 5);
			ctx.stroke();

			// Legs (from under dress)
			ctx.beginPath();
			ctx.moveTo(figure2X - 8, figureY + 25);
			ctx.lineTo(figure2X - 15, figureY + 50);
			ctx.stroke();
			ctx.beginPath();
			ctx.moveTo(figure2X + 8, figureY + 25);
			ctx.lineTo(figure2X + 15, figureY + 50);
			ctx.stroke();

			// Connect hands (holding hands)
			ctx.beginPath();
			ctx.moveTo(figure1X + 25, figureY - 5);
			ctx.lineTo(figure2X - 25, figureY - 5);
			ctx.stroke();

			// Add heart above them
			ctx.fillStyle = "#fff";
			ctx.font = "40px Arial";
			ctx.textAlign = "center";
			ctx.fillText("💕", centerX, figureY - 100);

			// Add text
			ctx.fillStyle = "#fff";
			ctx.shadowColor = "rgba(0, 0, 0, 0.5)";
			ctx.shadowBlur = 3;
			ctx.shadowOffsetX = 2;
			ctx.shadowOffsetY = 2;

			ctx.font = "bold 24px Arial";
			ctx.fillText("✨ Scratch to reveal! ✨", centerX, centerY + 100);

			// Reset shadow
			ctx.shadowColor = "transparent";
		}
	}, [scratchInView]);

	// Import the actual drawing and video
	const usDrawing = new URL("../assets/us.png", import.meta.url).href;
	const ourVideo = new URL("../assets/our_video.mp4", import.meta.url).href;

	// Import scratch card images
	const fakeScratchImage = new URL("../assets/fake_scratchcard_pic.jpg", import.meta.url).href;
	const realScratchImage = new URL("../assets/actual_scratchcard_pic.jpg", import.meta.url).href;

	// Scratch card functionality
	const handleScratch = (e) => {
		if (!scratchStarted) {
			setScratchStarted(true);
		}

		const canvas = scratchCanvasRef.current;
		if (!canvas || showFakeImage) return; // Stop scratching once reveal starts

		const rect = canvas.getBoundingClientRect();
		const scaleX = canvas.width / rect.width;
		const scaleY = canvas.height / rect.height;

		const x = ((e.clientX || e.touches?.[0]?.clientX) - rect.left) * scaleX;
		const y = ((e.clientY || e.touches?.[0]?.clientY) - rect.top) * scaleY;

		const ctx = canvas.getContext("2d");
		ctx.globalCompositeOperation = "destination-out";
		ctx.beginPath();
		ctx.arc(x, y, 30, 0, 2 * Math.PI);
		ctx.fill();

		// Calculate scratch progress more efficiently
		const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
		const pixels = imageData.data;
		let transparent = 0;
		const totalPixels = pixels.length / 4;

		// Sample every 4th pixel for better performance
		for (let i = 3; i < pixels.length; i += 16) {
			if (pixels[i] === 0) transparent++;
		}

		const progress = (transparent * 4) / totalPixels;
		setScratchProgress(progress);

		// Auto-reveal when 30% is scratched
		if (progress > 0.3 && !showFakeImage) {
			// Clear the entire canvas for smooth reveal
			ctx.globalCompositeOperation = "source-over";
			ctx.clearRect(0, 0, canvas.width, canvas.height);

			setShowFakeImage(true);
			setTimeout(() => {
				setShowJokingMessage(true);
				setTimeout(() => {
					setShowRealImage(true);
				}, 3500);
			}, 1000);
		}
	};

	const memories = [
		{
			title: "Our First Official Date",
			images: [
				"https://images.pexels.com/photos/1024969/pexels-photo-1024969.jpeg",
				"https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg",
				"https://images.pexels.com/photos/1855214/pexels-photo-1855214.jpeg",
			],
			description: "Remember the good parts of it more now no haha.",
		},
		{
			title: "Diwali",
			images: [
				"https://images.pexels.com/photos/3171837/pexels-photo-3171837.jpeg",
				"https://images.pexels.com/photos/1190297/pexels-photo-1190297.jpeg",
				"https://images.pexels.com/photos/1763075/pexels-photo-1763075.jpeg",
			],
			description:
				"When we flew those lanterns and you exclaiming it went sooooo far. It is a core memory for me. I won't ever forget it and I still remember that so clearly!",
		},
		{
			title: "Our first trip and my best birthday gift",
			images: [
				"https://images.pexels.com/photos/1405528/pexels-photo-1405528.jpeg",
				"https://images.pexels.com/photos/1139541/pexels-photo-1139541.jpeg",
				"https://images.pexels.com/photos/1834407/pexels-photo-1834407.jpeg",
			],
			description:
				"Beautiful stay. Prettiest girlfriend. Beach, candles and kisses under the moon. It was the best thing I have ever got. Period.",
		},
	];

	const reasons = [
		"Your smile",
		"Your imitation of me being down. So fucking funny, those puffed cheeks",
		"Your bites",
		"Your kisses",
		"Your neck",
		"Your childishness around me",
		"You leaving things to me and relaxing sometimes when you are with me",
		"You teasing me",
		"You telling me that you are proud of me",
		"You telling me I love you randomly",
		"You telling some outlandish future dreams sometimes and I somehow try to plan for them",
		"You planning future with me",
		"You holding my arm when walking",
		"You laying down on my chest and falling asleep. I will forever keep doing push‑ups for your pillow.",
		"You surprising me with visits and gifts",
		"You buying dresses and shoes for me. And you examining cutely after I wear them.",
		"You letting me pay or help you sometimes, which you don’t like doing with friends",
		"You ranting endlessly about sis, family and friends",
		"You trying to take good care of your health. By eating home food and doing things like Panch Karma",
		"You trying to take care of your family even though you mostly get annoyed by them",
		"You looking out and guiding your sister, helping her out in so many ways",
		"You taking responsibility for extra work with friends. During weddings or events etc.",
		"You standing up for yourself when disrespected",
		"Your liberal thinking",
		"Your wanting to be independent always",
		"Your wanting to be housewife also occasionally haha",
		"You wanting to be better and striving for better like always. Pushing yourself even though your friends are not that encouraging",
		"Your open‑mindedness and willingness to consider and think on new perspectives",
		"Your talent for planning things",
		"Your good to do good for people eventually",
		"Your pig face that you send sometimes. With those lips open thing haha. Ah it’s so cute",
		"You wanting me to look good and feeling proud that you made me look good. I love that.",
		"Your semi‑annoyed face when I forgot sunscreen or helmet",
		"Your eye when you wear that full–duratta while riding on scooty. They look so beautiful!",
		"You coming on top of me and looking at me like you are examining me",
		"You looking eye to eye with so much calmness and warmth and that slight smile, when you are lying besides me sometimes. I can feel so much love from inside of you.",
		"You wanting to watch Naruto with me. I never thought you would haha. Hmm I love that.",
		"You telling me, I am a nice man sometimes. That makes me want to be better.",
		"You being strong and getting back up despite difficult circumstances",
		"You defending Tejas even now. As much as I hate him, I do admire you somewhat for doing that still",
		"You being fair. You try to be fair with most people—family, friends or strangers",
		"Your obsession with cheap but numerous clothes haha",
		"You being interested in playing sports like cricket and badminton. And you play so well too",
		"Your reaction after you take first bite of some tasty sweet. It is so fucking cute.",
		"You defending Mysore as the best city haha",
		"You being the first person I always want to tell anything exciting to. Or even mundane things too.",
		"Your bond with your dad. I can feel that he respects you",
		"You wanting to be a hot aunty later lol",
		"You sucking my nipples. SO weird haha but also so cute.",
		"You teaching that South African kid despite no money promise",
		"Your bond with Monisha",
		"You writing each time what the payment is for when paying online. That discipline",
		"You negotiating bravely and giving ultimatums to Om. That was surprising to me",
		"You trying to confront difficult situations like telling Smaya after our talks",
		"You calling me Kutttu. I will never get tired hearing it",
		"You saying ewww to some shit I say",
		"You make me feel wanted. Not just immensely loved",
		"You calming me down when I feel tense sometimes, when lot of things come at once",
		"Your sleeping face. It is so cute",
		"You being health consicous, applying sunscreen etc. Somehow, now I cannot forget sunscreen even if I want to fucker haha. Thanks for that",
		"Your stubbornness. It is annoying to me sometimes haha—but it is good as well.",
		"Your coldness to your annoying relatives lol",
		"You taking me to Gokarna on my birthday. Your face while trekking Yana caves lol. Hmm and letting me do that pooja without saying anything or making me feel bad. That whole trip gift was the best thing I ever got. Those candles, that ocean sound. You looking so fucking beautiful. Surreal and unforgettable.",
		"You saying to me to come back soon after I go away for some days",
		"You wanting to stay up till I reach back after I drop you, even though you are very sleepy",
		"You doing random shit like that slow salute with one eye covered. It is so fucking cute",
		"You getting worried because of no double ticks in telegram if I don’t reply in lot of time",
		"You cooking together with me. I do want to learn some dishes from you.",
		"You feeling up my nipples. Especially on road lol",
		"You hugging me tight from behind sometimes, I love that feeling",
		"You trying to act interested when I am telling some boring information. And later sarcastically telling that, that info was useful etc ani. Fucker only haha but still cute.",
		"Your reactions when that lantern went far. I watched that video so so many times. One of my best memories with you.",
		"You gifting me one piece memorabilia",
		"You make me want to love you better and deeper.",
		"Your death stare when I do something annoying lol. So scary but still cute haha",
		"You coming to Interstellar with me.",
		"You taking care of me or being protective of me when I fell sick. I felt so happy when you sent that rasam you made for me that time",
		"You bringing up that I slept when you talk so many times lol. God forbid I slept once or twice by mistake haha",
		"Your possessiveness",
		"You not wanting to live an ordinary, routine life",
		"You wanting to work together on AI projects etc in future. I so want to do that with you.",
		"You writing goals and to‑do list offline rather than online",
		"You encouraging me to go after my goals",
		"You not being materialistic. And not wanting to show‑off with expensive things or big marriage function.",
		"You joking about what I said during fights like “‑Is that clear”",
		"Your talent for making me feel giddy like a teenager at times",
		"You resting your body on me in random positions when we are watching movies on bed. And you keep changing those positions haha",
		"You holding my hand under your face sometimes when you are sleeping and I am spooning. So fucking good",
		"You doing little things like offering your cheek signaling me to kiss...so naturally like it is normal",
		"You make normally boring things like cooking etc feel exciting when I am doing them with you",
		"You saying Manojuuu or Em chestannav ra on calls sometimes haha. So fucking cute",
		"You knowing instantly when I feel down and asking what is on my mind. You know me so well",
		"Your twisted sense of humor like offering snot in your hand to me.",
		"You not being able to sleep alone sometimes when I am still upset or awake and I don't come to bed. I am sorry I feel terrible about it but also feel loved by it somehow. I will try to do better about it.",
		"Your persistence about things like not eating fried foods",
		"Your passion to achieve something through working at ngos like Asthanya. I loved seeing those people's eyes, feeling like it'd be great if she joins us",
		"You asking me how much I love you or how much cute you are",
		"Your smile. I know I said it twice now. That’s how much I love it.",
		"You laughing sound when you really lose it",
		"Your smile fucker. Please smile now no.",
		"You",
	];

	const moods = [
		{
			image: "https://images.pexels.com/photos/1308885/pexels-photo-1308885.jpeg",
			mood: "Happy",
			description: "When your smile lights up the room 🌟",
		},
		{
			image: "https://images.pexels.com/photos/3808904/pexels-photo-3808904.jpeg",
			mood: "Silly",
			description: "Your goofy side that makes me laugh 😋",
		},
		{
			image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg",
			mood: "Thoughtful",
			description: "Lost in your beautiful thoughts 💭",
		},
		{
			image: "https://images.pexels.com/photos/1468379/pexels-photo-1468379.jpeg",
			mood: "Fierce",
			description: "When you're unstoppable 💪",
		},
	];

	const containerVariants = {
		hidden: { opacity: 0 },
		visible: {
			opacity: 1,
			transition: { staggerChildren: 0.3 },
		},
	};

	const itemVariants = {
		hidden: { y: 50, opacity: 0 },
		visible: {
			y: 0,
			opacity: 1,
			transition: {
				type: "spring",
				stiffness: 100,
				damping: 12,
			},
		},
	};

	const nextReason = () => {
		setCurrentReasonIndex((prev) => (prev + 1) % reasons.length);
	};

	const prevReason = () => {
		setCurrentReasonIndex((prev) => (prev - 1 + reasons.length) % reasons.length);
	};

	// Handle video replay when clicked
	const handleVideoReplay = () => {
		setVideoSettled(false);
		setShowVideoExplosion(true);
	};

	// Mobile swipe functionality for reasons
	const [touchStart, setTouchStart] = useState(null);
	const [touchEnd, setTouchEnd] = useState(null);

	// Night mode state
	const [isNightMode, setIsNightMode] = useState(false);

	const handleTouchStart = (e) => {
		setTouchEnd(null);
		setTouchStart(e.targetTouches[0].clientX);
	};

	const handleTouchMove = (e) => {
		setTouchEnd(e.targetTouches[0].clientX);
	};

	const handleTouchEnd = () => {
		if (!touchStart || !touchEnd) return;

		const distance = touchStart - touchEnd;
		const isLeftSwipe = distance > 50;
		const isRightSwipe = distance < -50;

		if (isLeftSwipe) {
			nextReason();
		}
		if (isRightSwipe) {
			prevReason();
		}
	};

	return (
		<div className={`birthday-main ${isNightMode ? "night-mode" : ""}`}>
			{/* Night Mode Toggle Button */}
			<motion.button
				className="night-mode-toggle"
				onClick={() => setIsNightMode(!isNightMode)}
				whileHover={{ scale: 1.1 }}
				whileTap={{ scale: 0.9 }}
				animate={{
					rotate: isNightMode ? 180 : 0,
				}}
				transition={{ duration: 0.5 }}>
				{isNightMode ? "☀️" : "🌙"}
			</motion.button>

			{/* Night Mode Stars */}
			{isNightMode && (
				<div className="night-stars">
					{[...Array(50)].map((_, i) => (
						<motion.div
							key={i}
							className="star"
							initial={{ opacity: 0, scale: 0 }}
							animate={{
								opacity: [0, 1, 0.3, 1],
								scale: [0, 1, 0.8, 1],
								transition: {
									duration: 2 + Math.random() * 3,
									delay: Math.random() * 2,
									repeat: Infinity,
									repeatType: "reverse",
								},
							}}
							style={{
								left: `${Math.random() * 100}%`,
								top: `${Math.random() * 100}%`,
							}}>
							✨
						</motion.div>
					))}
				</div>
			)}

			{/* Night Mode Fireflies */}
			{isNightMode && (
				<div className="fireflies">
					{[...Array(8)].map((_, i) => (
						<motion.div
							key={i}
							className="firefly"
							animate={{
								x: [0, Math.random() * 200 - 100, Math.random() * 300 - 150, 0],
								y: [0, Math.random() * 150 - 75, Math.random() * 200 - 100, 0],
								opacity: [0.3, 1, 0.5, 0.8, 0.3],
								scale: [0.8, 1.2, 0.9, 1.1, 0.8],
							}}
							transition={{
								duration: 8 + Math.random() * 4,
								delay: Math.random() * 3,
								repeat: Infinity,
								ease: "easeInOut",
							}}
							style={{
								left: `${10 + Math.random() * 80}%`,
								top: `${20 + Math.random() * 60}%`,
							}}>
							💫
						</motion.div>
					))}
				</div>
			)}

			{showConfetti && (
				<Confetti
					width={windowSize.width}
					height={windowSize.height}
					numberOfPieces={200}
					recycle={true}
					colors={["#ff69b4", "#ff1493", "#ff6b81", "#ff4d6d"]}
				/>
			)}

			{/* Ghibli-style Fireworks */}
			<GhibliFireworks />

			<Gallery>
				<motion.div className="content-container" variants={containerVariants} initial="hidden" animate="visible">
					<motion.div ref={headerRef} className="header-section" variants={itemVariants} animate={headerInView ? "visible" : "hidden"}>
						<motion.h1 animate={{ scale: [1, 1.08, 1] }} transition={{ repeat: Infinity, duration: 2 }}>
							Happy Birthday, My Love! 🎂
						</motion.h1>
						<p className="subtitle">Celebrating the most amazing person in my life</p>
					</motion.div>

					<motion.div ref={messageRef} className="message-section" variants={itemVariants} animate={messageInView ? "visible" : "hidden"}>
						<h2>My Kutti Kannamma ❤️</h2>
						<p>
							Every moment with you feels like a gift, and today I want to make it extra special. Your smile brightens my darkest days, and your
							love makes my heart skip a beat, even when it has been so long. Even when you are annoying sometimes haha, I just know I love you
							more than anything else in the world. Thank you for being the most wonderful partner I could ever wish for.
							<br />
							<br />I want to conquer the world for you and give you whatever you want. I want to achieve all the things I want, with you by my
							side. I want to make you feel like luckiest woman in the world and I want to feel like the luckiest man in the world.
							<br />
							<br />
							All of me looooooves all of you.
							<br />
							Love your curves and all your edges.
							<br />
							All your perfect imperfections.
							<br />
							Give your all to me. I will give my all to you.
							<br />
							All of me loves all of you.
							<br />
						</p>
					</motion.div>

					{/* Video Explosion Overlay */}
					<AnimatePresence>
						{showVideoExplosion && (
							<motion.div
								className="video-explosion-overlay"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								exit={{ opacity: 0 }}
								transition={{ duration: 0.3 }}>
								<motion.div
									className="explosion-content"
									initial={{ scale: 0, rotate: -180 }}
									animate={{
										scale: [0, 1.2, 1],
										rotate: [180, 0, 0],
										transition: {
											duration: 1.5,
											times: [0, 0.6, 1],
											type: "spring",
											stiffness: 200,
											damping: 15,
										},
									}}>
									<div className="explosion-sparkles">
										{[...Array(16)].map((_, i) => (
											<motion.div
												key={i}
												className="sparkle"
												initial={{ scale: 0, opacity: 0 }}
												animate={{
													scale: [0, 1, 0],
													opacity: [0, 1, 0],
													x: Math.cos((i * 22.5 * Math.PI) / 180) * 120,
													y: Math.sin((i * 22.5 * Math.PI) / 180) * 120,
												}}
												transition={{
													duration: 2,
													delay: 0.3 + i * 0.08,
													ease: "easeOut",
												}}>
												💕
											</motion.div>
										))}
									</div>
									<motion.div
										className="video-container-explosion"
										animate={{
											scale: [1, 1.02, 1],
											transition: {
												duration: 2,
												delay: 1,
												repeat: Infinity,
												repeatType: "reverse",
											},
										}}>
										{/* Floating side decorations */}
										<div className="video-side-decorations">
											{/* Left side floating hearts */}
											<div className="left-decorations">
												{[...Array(6)].map((_, i) => (
													<motion.div
														key={`left-${i}`}
														className="floating-heart"
														animate={{
															y: [0, -20, 0],
															x: [0, -10, 0],
															rotate: [0, 10, 0],
															scale: [1, 1.2, 1],
														}}
														transition={{
															duration: 3 + i * 0.5,
															delay: i * 0.8,
															repeat: Infinity,
															repeatType: "reverse",
														}}
														style={{
															position: "absolute",
															left: `-${60 + i * 15}px`,
															top: `${20 + i * 60}px`,
															fontSize: "1.5rem",
															zIndex: -1,
														}}>
														💕
													</motion.div>
												))}
											</div>

											{/* Right side floating sparkles */}
											<div className="right-decorations">
												{[...Array(6)].map((_, i) => (
													<motion.div
														key={`right-${i}`}
														className="floating-sparkle"
														animate={{
															y: [0, -15, 0],
															x: [0, 10, 0],
															rotate: [0, -15, 0],
															scale: [1, 1.3, 1],
														}}
														transition={{
															duration: 2.5 + i * 0.4,
															delay: i * 0.6,
															repeat: Infinity,
															repeatType: "reverse",
														}}
														style={{
															position: "absolute",
															right: `-${60 + i * 15}px`,
															top: `${30 + i * 55}px`,
															fontSize: "1.2rem",
															zIndex: -1,
														}}>
														{i % 2 === 0 ? "✨" : "🌟"}
													</motion.div>
												))}
											</div>
										</div>

										<video ref={videoRef} src={ourVideo} muted={false} className="video-explosion" controls />
										<motion.div
											className="video-overlay-text"
											initial={{ opacity: 0, y: 20 }}
											animate={{
												opacity: 1,
												y: 0,
												transition: { delay: 1.2 },
											}}>
											Our Beautiful Journey! 🎬✨
										</motion.div>
										<motion.div
											className="video-escape-hint"
											initial={{ opacity: 0 }}
											animate={{
												opacity: [0, 1, 0],
												transition: {
													delay: 3,
													duration: 2,
													repeat: Infinity,
													repeatDelay: 3,
												},
											}}>
											Press ESC to continue browsing 💕
										</motion.div>
									</motion.div>
								</motion.div>
							</motion.div>
						)}
					</AnimatePresence>

					{/* Settled Video between message and counter */}
					<AnimatePresence>
						{videoSettled && (
							<motion.div
								className="video-container-settled"
								initial={{
									scale: 1.3,
									y: -50,
									opacity: 0,
								}}
								animate={{
									scale: 1,
									y: 0,
									opacity: 1,
									transition: {
										duration: 1.5,
										type: "spring",
										stiffness: 100,
										damping: 20,
									},
								}}>
								<motion.div
									className="video-wrapper"
									animate={{
										rotate: [0, 0.2, -0.2, 0],
										transition: {
											duration: 12,
											repeat: Infinity,
											ease: "easeInOut",
										},
									}}
									onClick={handleVideoReplay}
									style={{ cursor: "pointer" }}
									whileHover={{ scale: 1.05 }}
									whileTap={{ scale: 0.95 }}>
									<video src={ourVideo} autoPlay muted loop className="video-settled" />
									<motion.div
										className="video-hearts"
										animate={{
											scale: [1, 1.2, 1],
											opacity: [0.6, 1, 0.6],
											transition: {
												duration: 3,
												repeat: Infinity,
												repeatType: "reverse",
											},
										}}>
										💕💖💕
									</motion.div>
								</motion.div>
								<motion.p
									className="video-caption"
									initial={{ opacity: 0, y: 10 }}
									animate={{
										opacity: 1,
										y: 0,
										transition: { delay: 1 },
									}}>
									Our Beautiful Journey Together! 🎁💕
								</motion.p>
							</motion.div>
						)}
					</AnimatePresence>

					<motion.div ref={counterRef} className="counter-section" variants={itemVariants} animate={counterInView ? "visible" : "hidden"}>
						<motion.h2
							animate={{
								scale: [1, 1.05, 1],
								color: ["#be185d", "#ec4899", "#be185d"],
							}}
							transition={{
								duration: 2,
								repeat: Infinity,
								repeatType: "reverse",
							}}>
							Days Since You Stole My Heart 💕
						</motion.h2>
						<motion.div
							className="counter-display"
							animate={
								counterFinished
									? {
											scale: [1, 1.1, 1],
											transition: {
												duration: 0.5,
												repeat: 2,
												repeatType: "reverse",
											},
									  }
									: {}
							}>
							<motion.div
								className="counter-number"
								key={counterValue}
								initial={{ scale: 0.8, opacity: 0.7 }}
								animate={{ scale: 1, opacity: 1 }}
								transition={{
									type: "spring",
									stiffness: 300,
									damping: 20,
								}}>
								{counterValue}
							</motion.div>
							<motion.p className="counter-subtitle" initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5 }}>
								{counterFinished ? "Beautiful days together! 🌟" : "Counting our love..."}
							</motion.p>
						</motion.div>
						<AnimatePresence>
							{counterFinished && (
								<motion.p
									className="counter-date"
									initial={{ opacity: 0, y: 20, scale: 0.8 }}
									animate={{
										opacity: 1,
										y: 0,
										scale: 1,
										transition: {
											type: "spring",
											stiffness: 200,
											damping: 20,
											delay: 0.5,
										},
									}}
									exit={{ opacity: 0, scale: 0.8 }}>
									Since June 21, 2024 (Our first kiss 😘)
								</motion.p>
							)}
						</AnimatePresence>
					</motion.div>

					<motion.div ref={memoriesRef} className="memories-section" variants={itemVariants} animate={memoriesInView ? "visible" : "hidden"}>
						<h2>Our Beautiful Memories 📸</h2>
						<div className="memories-stack">
							{memories.map((memory, index) => (
								<motion.div
									key={index}
									className="memory-card"
									initial={{ rotate: -10 + Math.random() * 20 }}
									whileHover={{
										rotate: 0,
										scale: 1.05,
										zIndex: 1,
										transition: { type: "spring", stiffness: 300 },
									}}>
									<h3>{memory.title}</h3>
									<div className="memory-images">
										{memory.images.map((image, imgIndex) => (
											<Item key={imgIndex} original={image} thumbnail={image} width="1200" height="800">
												{({ ref, open }) => (
													<motion.img
														ref={ref}
														onClick={open}
														src={image}
														alt={`Memory ${index + 1}`}
														whileHover={{ scale: 1.05 }}
														transition={{ type: "spring", stiffness: 300 }}
													/>
												)}
											</Item>
										))}
									</div>
									<p>{memory.description}</p>
								</motion.div>
							))}
						</div>
					</motion.div>

					<motion.div ref={reasonsRef} className="reasons-section" variants={itemVariants} animate={reasonsInView ? "visible" : "hidden"}>
						<div className="reasons-title-container">
							<AnimatePresence mode="wait">
								{!surpriseRevealed ? (
									<motion.h2
										key="original"
										initial={{ opacity: 1 }}
										exit={{
											opacity: 0,
											scale: 0.8,
											transition: { duration: 0.5 },
										}}
										className="reasons-title">
										80 Reasons Why I Love You 💝
									</motion.h2>
								) : (
									<motion.h2
										key="surprise"
										initial={{ opacity: 0, y: 20 }}
										animate={{
											opacity: 1,
											y: 0,
											transition: {
												delay: 0.3,
												type: "spring",
												stiffness: 200,
												damping: 20,
											},
										}}
										className="reasons-title">
										<span className="crossed-out">80</span>
										<motion.span
											className="surprise-number"
											initial={{ scale: 0, rotate: -10 }}
											animate={{
												scale: 1,
												rotate: 0,
												transition: {
													delay: 0.8,
													type: "spring",
													stiffness: 300,
													damping: 15,
												},
											}}>
											100
										</motion.span>
										<span> Reasons Why I Love You 💝</span>
										<motion.span
											className="surprise-text"
											initial={{ opacity: 0, scale: 0.5 }}
											animate={{
												opacity: 1,
												scale: 1,
												transition: {
													delay: 1.2,
													type: "spring",
													stiffness: 200,
												},
											}}>
											✨ Surprise! I added 20 more! ✨
										</motion.span>
									</motion.h2>
								)}
							</AnimatePresence>
						</div>

						{/* Drawing Explosion Overlay */}
						<AnimatePresence>
							{showDrawingExplosion && !drawingSettled && (
								<motion.div
									className="drawing-explosion-overlay"
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									exit={{ opacity: 0 }}
									transition={{ duration: 0.3 }}>
									<motion.div
										className="explosion-content"
										initial={{ scale: 0, rotate: -180 }}
										animate={{
											scale: [0, 1.5, 1],
											rotate: [180, 0, 0],
											transition: {
												duration: 1.5,
												times: [0, 0.6, 1],
												type: "spring",
												stiffness: 200,
												damping: 15,
											},
										}}>
										<div className="explosion-sparkles">
											{[...Array(12)].map((_, i) => (
												<motion.div
													key={i}
													className="sparkle"
													initial={{ scale: 0, opacity: 0 }}
													animate={{
														scale: [0, 1, 0],
														opacity: [0, 1, 0],
														x: Math.cos((i * 30 * Math.PI) / 180) * 100,
														y: Math.sin((i * 30 * Math.PI) / 180) * 100,
													}}
													transition={{
														duration: 1.5,
														delay: 0.5 + i * 0.1,
														ease: "easeOut",
													}}>
													✨
												</motion.div>
											))}
										</div>
										<motion.div
											className="drawing-container-explosion"
											animate={{
												scale: [1, 1.1, 1],
												transition: {
													duration: 0.5,
													delay: 1,
													repeat: 2,
													repeatType: "reverse",
												},
											}}>
											<img src={usDrawing} alt="Us together" className="drawing-image-explosion" />
										</motion.div>
									</motion.div>
								</motion.div>
							)}
						</AnimatePresence>

						{/* Settled Drawing above reasons */}
						<AnimatePresence>
							{drawingSettled && (
								<motion.div
									className="drawing-container-settled"
									initial={{
										scale: 1.5,
										y: -100,
										opacity: 0,
									}}
									animate={{
										scale: 1,
										y: 0,
										opacity: 1,
										transition: {
											duration: 1.5,
											type: "spring",
											stiffness: 100,
											damping: 20,
										},
									}}>
									<motion.div
										animate={{
											rotate: [0, 1, -1, 0],
											transition: {
												duration: 4,
												repeat: Infinity,
												ease: "easeInOut",
											},
										}}>
										<img src={usDrawing} alt="Us together" className="drawing-image-settled" />
									</motion.div>
									<motion.p
										className="drawing-caption"
										initial={{ opacity: 0, y: 10 }}
										animate={{
											opacity: 1,
											y: 0,
											transition: { delay: 1 },
										}}>
										Us! 💕
									</motion.p>
								</motion.div>
							)}
						</AnimatePresence>

						{/* Reasons Gallery */}
						<div className="reasons-gallery">
							<motion.button className="gallery-nav prev" onClick={prevReason} whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
								❮
							</motion.button>

							<AnimatePresence mode="wait">
								<motion.div
									key={currentReasonIndex}
									className="reason-display"
									initial={{ opacity: 0, x: 50 }}
									animate={{ opacity: 1, x: 0 }}
									exit={{ opacity: 0, x: -50 }}
									transition={{ type: "spring", stiffness: 300, damping: 30 }}>
									<motion.div
										className="reason-card-large"
										whileHover={{ scale: 1.02 }}
										onTouchStart={handleTouchStart}
										onTouchMove={handleTouchMove}
										onTouchEnd={handleTouchEnd}
										onClick={nextReason}
										whileTap={{ scale: 0.98 }}>
										<div className="reason-number-large">
											{currentReasonIndex + 1}/{surpriseRevealed ? reasons.length : "80"}
										</div>
										<p className="reason-text">{reasons[currentReasonIndex]}</p>
									</motion.div>
								</motion.div>
							</AnimatePresence>

							<motion.button className="gallery-nav next" onClick={nextReason} whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
								❯
							</motion.button>
						</div>
					</motion.div>

					{/* Scratch Card Section */}
					<motion.div ref={scratchRef} className="scratch-section" variants={itemVariants} animate={scratchInView ? "visible" : "hidden"}>
						<motion.h2
							animate={{
								scale: [1, 1.05, 1],
								color: ["#be185d", "#ec4899", "#be185d"],
							}}
							transition={{
								duration: 2,
								repeat: Infinity,
								repeatType: "reverse",
							}}>
							You know that picture you liked so much? Scratch it off to see! 🎁
						</motion.h2>

						<div className="scratch-container">
							<motion.div
								className="scratch-card-wrapper"
								animate={{
									scale: scratchCardScale,
								}}
								transition={{
									type: "spring",
									stiffness: 200,
									damping: 20,
								}}>
								<div className="scratch-card">
									{/* Scratch overlay canvas */}
									<canvas
										ref={scratchCanvasRef}
										className="scratch-canvas"
										width={400}
										height={480}
										onMouseDown={(e) => {
											e.preventDefault();
											setIsScratching(true);
											handleScratch(e);
										}}
										onMouseMove={(e) => {
											e.preventDefault();
											if (isScratching) handleScratch(e);
										}}
										onMouseUp={(e) => {
											e.preventDefault();
											setIsScratching(false);
										}}
										onMouseLeave={(e) => {
											e.preventDefault();
											setIsScratching(false);
										}}
										onTouchStart={(e) => {
											e.preventDefault();
											setIsScratching(true);
											handleScratch(e);
										}}
										onTouchMove={(e) => {
											e.preventDefault();
											if (isScratching) handleScratch(e);
										}}
										onTouchEnd={(e) => {
											e.preventDefault();
											setIsScratching(false);
										}}
										style={{ touchAction: "none" }}
									/>

									{/* Background images */}
									<div className="scratch-background">
										<AnimatePresence mode="wait">
											{!showFakeImage && !showRealImage && (
												<motion.div
													className="scratch-hint"
													animate={{
														scale: [1, 1.1, 1],
														opacity: [0.7, 1, 0.7],
													}}
													transition={{
														duration: 2,
														repeat: Infinity,
													}}>
													<p>✨ Scratch here to reveal! ✨</p>
												</motion.div>
											)}

											{showFakeImage && !showRealImage && (
												<motion.div
													key="fake"
													initial={{ opacity: 0, scale: 0.8 }}
													animate={{ opacity: 1, scale: 1 }}
													exit={{ opacity: 0, scale: 0.8 }}
													className="scratch-image-container">
													<img src={fakeScratchImage} alt="Fake surprise" className="scratch-image" />
												</motion.div>
											)}

											{showRealImage && (
												<motion.div
													key="real"
													initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
													animate={{
														opacity: 1,
														scale: 1,
														rotate: 0,
														transition: {
															type: "spring",
															stiffness: 200,
															damping: 15,
														},
													}}
													className="scratch-image-container">
													<img src={realScratchImage} alt="Real surprise" className="scratch-image" />
													<motion.div
														className="scratch-hearts"
														animate={{
															scale: [1, 1.2, 1],
															opacity: [0.6, 1, 0.6],
														}}
														transition={{
															duration: 3,
															repeat: Infinity,
															repeatType: "reverse",
														}}>
														💕💖💕
													</motion.div>
												</motion.div>
											)}
										</AnimatePresence>
									</div>
								</div>

								{/* Messages positioned relative to the card */}
								<div className="scratch-messages">
									{/* Joking message */}
									<AnimatePresence>
										{showJokingMessage && !showRealImage && (
											<motion.div
												className="joking-message"
												initial={{ opacity: 0, y: 20, scale: 0.8 }}
												animate={{
													opacity: 1,
													y: 0,
													scale: 1,
													transition: {
														type: "spring",
														stiffness: 300,
														damping: 20,
													},
												}}
												exit={{ opacity: 0, y: -20, scale: 0.8 }}>
												<motion.h3
													animate={{
														rotate: [0, 5, -5, 0],
														scale: [1, 1.1, 1],
													}}
													transition={{
														duration: 0.5,
														repeat: 3,
													}}>
													JUST KIDDING! 😂😂😂
												</motion.h3>
												<p>Wait for the real surprise... 💕</p>
											</motion.div>
										)}
									</AnimatePresence>

									{/* Final message */}
									<AnimatePresence>
										{showRealImage && (
											<motion.div
												className="scratch-final-message"
												initial={{ opacity: 0, y: 30 }}
												animate={{
													opacity: 1,
													y: 0,
													transition: { delay: 1 },
												}}>
												<motion.p
													animate={{
														color: ["#be185d", "#ec4899", "#be185d"],
													}}
													transition={{
														duration: 2,
														repeat: Infinity,
														repeatType: "reverse",
													}}>
													We in our own little world! ❤️
												</motion.p>
											</motion.div>
										)}
									</AnimatePresence>
								</div>
							</motion.div>
						</div>
					</motion.div>

					<motion.div ref={moodsRef} className="moods-section" variants={itemVariants} animate={moodsInView ? "visible" : "hidden"}>
						<h2>All Your Beautiful Moods 🌈</h2>
						<div className="moods-container">
							{moods.map((mood, index) => (
								<motion.div
									key={index}
									className="mood-card"
									initial={{ rotate: -5 + Math.random() * 10 }}
									whileHover={{
										rotate: 0,
										scale: 1.05,
										zIndex: 1,
									}}>
									<img src={mood.image} alt={mood.mood} />
									<div className="mood-content">
										<h3>{mood.mood}</h3>
										<p>{mood.description}</p>
									</div>
									<motion.div
										className="mood-arrow"
										animate={{
											x: [0, 10, 0],
											rotate: [0, 5, 0],
										}}
										transition={{
											duration: 2,
											repeat: Infinity,
											repeatType: "reverse",
										}}>
										➜
									</motion.div>
								</motion.div>
							))}
						</div>
						<div className="moods-footer">
							<motion.h3
								animate={{
									scale: [1, 1.1, 1],
									color: ["#be185d", "#ec4899", "#be185d"],
								}}
								transition={{
									duration: 3,
									repeat: Infinity,
									repeatType: "reverse",
								}}>
								And I Love Every Single One! 💖
							</motion.h3>
						</div>
					</motion.div>

					<motion.div className="final-note" variants={itemVariants} whileHover={{ scale: 1.02 }}>
						<motion.h2
							animate={{
								color: ["#be185d", "#ec4899", "#be185d"],
								scale: [1, 1.05, 1],
							}}
							transition={{
								duration: 3,
								repeat: Infinity,
								repeatType: "reverse",
							}}>
							Forever Yours 💝
						</motion.h2>
						<p>I love you more than words can express. Here's to many more birthdays together!</p>
					</motion.div>
				</motion.div>
			</Gallery>
		</div>
	);
}

export default BirthdayMain;
