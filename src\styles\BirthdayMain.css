.birthday-main {
	min-height: 100vh;
	background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
	padding: var(--space-4);
	overflow-x: hidden;
	position: relative;
}

.content-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: var(--space-4);
	position: relative;
	z-index: 1;
}

.header-section {
	text-align: center;
	margin-bottom: var(--space-8);
	position: relative;
}

.header-section h1 {
	font-size: 4rem;
	background: linear-gradient(45deg, var(--primary-600), var(--primary-400));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	margin-bottom: var(--space-2);
	filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.subtitle {
	font-size: 1.8rem;
	color: var(--primary-600);
	font-family: var(--font-heading);
}

.message-section {
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-6);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	margin-bottom: var(--space-8);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-section h2 {
	color: var(--primary-600);
	margin-bottom: var(--space-4);
	font-size: 2.5rem;
}

.message-section p {
	font-size: 1.4rem;
	line-height: 1.8;
	color: var(--neutral-700);
	font-family: var(--font-heading);
}

.counter-section {
	text-align: center;
	margin-bottom: var(--space-8);
	padding: var(--space-6);
	background: rgba(255, 255, 255, 0.9);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.counter-section h2 {
	color: var(--primary-600);
	margin-bottom: var(--space-6);
	font-size: 2.5rem;
}

.counter-display {
	margin-bottom: var(--space-4);
}

.counter-number {
	font-size: 6rem;
	font-weight: bold;
	background: linear-gradient(45deg, var(--primary-600), var(--primary-400));
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;
	margin-bottom: var(--space-2);
	filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
	font-family: var(--font-heading);
	line-height: 1;
}

.counter-subtitle {
	font-size: 1.6rem;
	color: var(--primary-600);
	font-family: var(--font-heading);
	margin: 0;
	font-weight: 600;
}

.counter-date {
	font-size: 1.2rem;
	color: var(--neutral-600);
	font-family: var(--font-heading);
	margin: 0;
	font-style: italic;
}

.memories-section {
	margin-bottom: var(--space-8);
}

.memories-section h2 {
	text-align: center;
	color: var(--primary-600);
	margin-bottom: var(--space-6);
	font-size: 2.5rem;
}

.memories-stack {
	position: relative;
	padding: var(--space-4);
	perspective: 1000px;
}

.memory-card {
	background: rgba(255, 255, 255, 0.9);
	border-radius: var(--radius-lg);
	padding: var(--space-4);
	margin-bottom: var(--space-4);
	box-shadow: var(--shadow-lg);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
	cursor: pointer;
}

.memory-card h3 {
	color: var(--primary-600);
	margin-bottom: var(--space-3);
	font-size: 1.8rem;
	text-align: center;
}

.memory-images {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: var(--space-3);
	margin-bottom: var(--space-3);
}

.memory-images img {
	width: 100%;
	height: 200px;
	object-fit: cover;
	border-radius: var(--radius-md);
	cursor: pointer;
	transition: all 0.3s ease;
}

.memory-card p {
	color: var(--neutral-700);
	font-size: 1.2rem;
	text-align: center;
	font-family: var(--font-heading);
}

.reasons-section {
	margin-bottom: var(--space-8);
	padding: var(--space-4);
}

.reasons-title-container {
	text-align: center;
	margin-bottom: var(--space-6);
	min-height: 120px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.reasons-title {
	color: var(--primary-600);
	font-size: 2.5rem;
	margin: 0;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: var(--space-2);
}

.crossed-out {
	position: relative;
	color: var(--neutral-400);
}

.crossed-out::after {
	content: "";
	position: absolute;
	left: 0;
	top: 50%;
	width: 100%;
	height: 3px;
	background: #ff4444;
	transform: translateY(-50%) rotate(-5deg);
	animation: drawLine 0.8s ease-out forwards;
}

@keyframes drawLine {
	from {
		width: 0;
	}
	to {
		width: 100%;
	}
}

.surprise-number {
	color: var(--primary-500);
	font-weight: bold;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
	display: inline-block;
}

.surprise-text {
	font-size: 1.2rem;
	color: var(--primary-500);
	font-weight: 600;
	margin-top: var(--space-2);
	display: block;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* Drawing Explosion Styles */
.drawing-explosion-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10px);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.explosion-content {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.explosion-sparkles {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.sparkle {
	position: absolute;
	font-size: 1.5rem;
	pointer-events: none;
}

.drawing-container-explosion {
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-6);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	border: 3px solid var(--primary-200);
}

.drawing-image-explosion {
	max-width: 300px;
	max-height: 250px;
	width: auto;
	height: auto;
	border-radius: var(--radius-md);
	box-shadow: var(--shadow-md);
}

/* Settled Drawing Styles */
.drawing-container-settled {
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-4);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	border: 2px solid var(--primary-200);
	text-align: center;
	margin: 0 auto var(--space-6) auto;
	max-width: 400px;
	backdrop-filter: blur(10px);
}

.drawing-image-settled {
	max-width: 250px;
	max-height: 200px;
	width: auto;
	height: auto;
	border-radius: var(--radius-md);
	box-shadow: var(--shadow-sm);
}

.drawing-caption {
	margin-top: var(--space-2);
	font-size: 1.1rem;
	color: var(--primary-600);
	font-weight: 600;
	margin-bottom: 0;
}

/* Video Explosion Styles */
.video-explosion-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 182, 193, 0.95), rgba(255, 192, 203, 0.95));
	backdrop-filter: blur(15px);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.video-container-explosion {
	background: rgba(255, 255, 255, 0.95);
	padding: var(--space-6);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	border: 3px solid var(--primary-200);
	position: relative;
	overflow: hidden;
}

.video-explosion {
	max-width: 600px;
	max-height: 80vh;
	width: auto;
	height: auto;
	border-radius: var(--radius-md);
	box-shadow: var(--shadow-lg);
}

.video-overlay-text {
	position: absolute;
	bottom: -50px;
	left: 50%;
	transform: translateX(-50%);
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-2) var(--space-4);
	border-radius: var(--radius-full);
	font-size: 1.2rem;
	color: var(--primary-600);
	font-weight: 600;
	white-space: nowrap;
	box-shadow: var(--shadow-sm);
}

.video-escape-hint {
	position: absolute;
	top: -40px;
	left: 50%;
	transform: translateX(-50%);
	background: rgba(255, 255, 255, 0.95);
	padding: var(--space-2) var(--space-3);
	border-radius: var(--radius-full);
	font-size: 0.9rem;
	color: var(--primary-500);
	font-weight: 500;
	white-space: nowrap;
	box-shadow: var(--shadow-sm);
	border: 1px solid var(--primary-200);
}

.video-side-decorations {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	overflow: hidden;
}

.left-decorations,
.right-decorations {
	position: absolute;
	top: 0;
	height: 100%;
}

.floating-heart,
.floating-sparkle {
	position: absolute;
	pointer-events: none;
	user-select: none;
	opacity: 0.8;
}

/* Settled Video Styles */
.video-container-settled {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 182, 193, 0.1));
	padding: var(--space-8);
	border-radius: var(--radius-xl);
	box-shadow: var(--shadow-lg);
	border: 2px solid var(--primary-200);
	text-align: center;
	margin: var(--space-8) auto;
	max-width: 700px;
	min-height: 450px;
	backdrop-filter: blur(10px);
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: var(--space-6);
}

.video-wrapper {
	position: relative;
	display: inline-block;
}

.video-settled {
	max-width: 500px;
	max-height: 350px;
	width: auto;
	height: auto;
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
}

.video-hearts {
	position: absolute;
	top: -20px;
	right: -20px;
	font-size: 1.5rem;
	pointer-events: none;
}

.video-caption {
	margin-top: var(--space-4);
	font-size: 2.2rem;
	color: var(--primary-600);
	font-weight: 700;
	margin-bottom: 0;
	font-family: var(--font-heading);
	line-height: 1.3;
	text-align: center;
}

.reasons-gallery {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: var(--space-4);
	margin-bottom: var(--space-4);
	position: relative;
	min-height: 300px;
}

.gallery-nav {
	background: rgba(255, 255, 255, 0.9);
	border: none;
	border-radius: 50%;
	width: 50px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.5rem;
	color: var(--primary-600);
	cursor: pointer;
	box-shadow: var(--shadow-md);
	z-index: 2;
}

.reason-display {
	flex: 1;
	max-width: 600px;
	perspective: 1000px;
}

.reason-card-large {
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-6);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	position: relative;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	min-height: 200px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.reason-number-large {
	position: absolute;
	top: var(--space-3);
	left: var(--space-3);
	background: var(--primary-500);
	color: white;
	padding: var(--space-2) var(--space-3);
	border-radius: var(--radius-full);
	font-size: 0.9rem;
}

.reason-text {
	font-size: 1.8rem;
	color: var(--primary-700);
	text-align: center;
	font-family: var(--font-heading);
	margin: 0;
	padding: var(--space-4);
}

.reasons-preview {
	text-align: center;
	color: var(--primary-600);
	font-size: 1.2rem;
	margin-top: var(--space-4);
}

.moods-section {
	margin-bottom: var(--space-8);
	padding: var(--space-4);
}

.moods-section h2 {
	text-align: center;
	color: var(--primary-600);
	margin-bottom: var(--space-6);
	font-size: 2.5rem;
}

.moods-container {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
	gap: var(--space-6);
	padding: var(--space-4);
}

.mood-card {
	background: white;
	border-radius: var(--radius-lg);
	overflow: hidden;
	box-shadow: var(--shadow-lg);
	position: relative;
	transition: all 0.3s ease;
}

.mood-card img {
	width: 100%;
	height: 300px;
	object-fit: cover;
}

.mood-content {
	padding: var(--space-4);
	text-align: center;
}

.mood-content h3 {
	color: var(--primary-600);
	margin-bottom: var(--space-2);
	font-size: 1.8rem;
}

.mood-content p {
	color: var(--neutral-700);
	font-size: 1.1rem;
}

.mood-arrow {
	position: absolute;
	top: 50%;
	right: var(--space-4);
	font-size: 2rem;
	color: var(--primary-500);
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.moods-footer {
	text-align: center;
	margin-top: var(--space-6);
}

.moods-footer h3 {
	font-size: 2rem;
	color: var(--primary-600);
	display: inline-block;
	padding: var(--space-4);
	background: rgba(255, 255, 255, 0.9);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
}

.birthday-main::before {
	content: "";
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(circle at 10% 20%, rgba(255, 105, 180, 0.1) 0%, transparent 20%),
		radial-gradient(circle at 90% 30%, rgba(236, 72, 153, 0.1) 0%, transparent 20%),
		radial-gradient(circle at 30% 70%, rgba(219, 39, 119, 0.1) 0%, transparent 20%),
		radial-gradient(circle at 70% 80%, rgba(190, 24, 93, 0.1) 0%, transparent 20%);
	pointer-events: none;
	z-index: 0;
}

.pswp__bg {
	background: rgba(0, 0, 0, 0.9) !important;
}

/* Ghibli-style Fireworks */
.ghibli-fireworks {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1;
}

.firework-container {
	position: relative;
}

.firework-burst {
	position: relative;
	width: 400px;
	height: 400px;
}

.firework-particle {
	border-radius: 50%;
	filter: blur(0.5px);
}

.firework-sparkle {
	filter: drop-shadow(0 0 2px currentColor);
}

.firework-trail {
	border-radius: 50%;
	filter: blur(0.3px);
}

.firework-glow {
	filter: blur(2px);
}

/* Scratch Card Section */
.scratch-section {
	margin-bottom: var(--space-8);
	padding: var(--space-6);
	text-align: center;
}

.scratch-section h2 {
	color: var(--primary-600);
	margin-bottom: var(--space-6);
	font-size: 2.5rem;
}

.scratch-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: var(--space-6);
	padding: var(--space-6);
	background: rgba(255, 255, 255, 0.05);
	border-radius: var(--radius-xl);
	backdrop-filter: blur(10px);
}

.scratch-card-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
	transform-origin: center;
}

.scratch-card {
	position: relative;
	width: 400px;
	height: 480px;
	border-radius: var(--radius-xl);
	overflow: hidden;
	box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.2);
	background: linear-gradient(135deg, var(--primary-100), var(--accent-100));
	border: 3px solid rgba(255, 255, 255, 0.3);
}

.scratch-messages {
	margin-top: var(--space-4);
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: var(--space-3);
}

.scratch-canvas {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	cursor: crosshair;
	z-index: 2;
	border-radius: var(--radius-lg);
	touch-action: none;
	user-select: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
}

.scratch-background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1;
}

.scratch-hint {
	color: var(--primary-600);
	font-size: 1.5rem;
	font-weight: bold;
	text-align: center;
}

.scratch-image-container {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.scratch-image {
	max-width: 95%;
	max-height: 95%;
	object-fit: cover;
	border-radius: var(--radius-lg);
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
	border: 3px solid rgba(255, 255, 255, 0.8);
}

.scratch-hearts {
	position: absolute;
	top: 10px;
	right: 10px;
	font-size: 1.5rem;
	z-index: 3;
}

.joking-message {
	background: rgba(255, 255, 255, 0.95);
	padding: var(--space-3);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	backdrop-filter: blur(10px);
	border: 2px solid var(--primary-200);
	max-width: 350px;
	text-align: center;
}

.joking-message h3 {
	color: var(--primary-600);
	margin-bottom: var(--space-2);
	font-size: 1.8rem;
}

.joking-message p {
	color: var(--neutral-700);
	font-size: 1.1rem;
	margin: 0;
}

.scratch-final-message {
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-3);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	backdrop-filter: blur(10px);
	border: 2px solid var(--primary-200);
	max-width: 350px;
	text-align: center;
}

.scratch-final-message p {
	font-size: 1.3rem;
	font-weight: bold;
	margin: 0;
}

@media (max-width: 768px) {
	/* Reduce overall page padding for more card space */
	.birthday-main {
		padding: var(--space-2);
	}

	.header-section h1 {
		font-size: 2.5rem;
	}

	.subtitle {
		font-size: 1.4rem;
	}

	.message-section,
	.final-note {
		padding: var(--space-4);
		margin-left: var(--space-1);
		margin-right: var(--space-1);
	}

	.memory-images {
		grid-template-columns: 1fr;
	}

	.memory-images img {
		height: 250px;
	}

	.message-section p,
	.final-note p {
		font-size: 1.2rem;
	}

	.wishes-list {
		grid-template-columns: 1fr;
	}

	/* Mobile reasons section - stack of papers design */
	.reasons-gallery {
		flex-direction: column;
		gap: 0;
		margin-bottom: var(--space-4);
		position: relative;
		min-height: 250px;
		perspective: 1000px;
	}

	.gallery-nav {
		display: none; /* Hide arrows on mobile */
	}

	.reason-display {
		flex: 1;
		max-width: none;
		width: 100%;
		position: relative;
	}

	.reason-card-large {
		padding: var(--space-4);
		min-height: 200px;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		cursor: pointer;
		user-select: none;
		touch-action: pan-y;
		transform-origin: center bottom;
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
		border: 2px solid rgba(255, 255, 255, 0.8);
	}

	/* Stack effect for mobile */
	.reason-card-large:nth-child(1) {
		z-index: 3;
		transform: translateY(0px) rotate(0deg);
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 182, 193, 0.1));
	}

	.reason-card-large:nth-child(2) {
		z-index: 2;
		transform: translateY(8px) rotate(-1deg) scale(0.98);
		opacity: 0.9;
		background: rgba(255, 255, 255, 0.85);
	}

	.reason-card-large:nth-child(3) {
		z-index: 1;
		transform: translateY(16px) rotate(1deg) scale(0.96);
		opacity: 0.8;
		background: rgba(255, 255, 255, 0.75);
	}

	/* Add swipe hint */
	.reasons-preview {
		margin-top: var(--space-6);
		position: relative;
	}

	.reasons-preview::before {
		content: "👆";
		position: absolute;
		top: -30px;
		left: 50%;
		transform: translateX(-50%);
		font-size: 1.5rem;
		animation: bounce 2s infinite;
	}

	.reason-text {
		font-size: 1.4rem;
	}

	.reasons-title {
		font-size: 2rem;
	}

	.surprise-text {
		font-size: 1rem;
	}

	.reasons-title-container {
		min-height: 100px;
	}

	/* Mobile drawing styles */
	.drawing-container-settled {
		max-width: 300px;
		padding: var(--space-3);
	}

	/* Mobile scratch card styles */
	.scratch-section h2 {
		font-size: 1.8rem;
	}

	.scratch-card {
		width: 300px;
		height: 360px;
	}

	.scratch-canvas {
		width: 300px;
		height: 360px;
	}

	.scratch-hint {
		font-size: 1.2rem;
	}

	.joking-message {
		max-width: 280px;
		padding: var(--space-2);
	}

	.joking-message h3 {
		font-size: 1.4rem;
	}

	.joking-message p {
		font-size: 0.9rem;
	}

	.scratch-final-message {
		max-width: 280px;
		padding: var(--space-2);
	}

	.scratch-final-message p {
		font-size: 1.1rem;
	}

	/* Reduce firework intensity on mobile for better performance */
	.ghibli-fireworks {
		opacity: 0.8;
	}

	.firework-burst {
		width: 300px;
		height: 300px;
	}

	.drawing-image-settled {
		max-width: 200px;
		max-height: 150px;
	}

	/* Mobile counter styles */
	.counter-section h2 {
		font-size: 2rem;
	}

	.counter-number {
		font-size: 4rem;
	}

	.counter-subtitle {
		font-size: 1.3rem;
	}

	.counter-date {
		font-size: 1rem;
	}

	/* Mobile video styles */
	.video-container-explosion {
		max-width: 90vw;
		padding: var(--space-3);
	}

	.video-explosion {
		max-width: 85vw;
		max-height: 70vh;
	}

	.video-overlay-text {
		font-size: 1rem;
		bottom: -40px;
	}

	.video-escape-hint {
		font-size: 0.8rem;
		top: -35px;
	}

	.video-container-settled {
		max-width: 90%;
		padding: var(--space-6);
		min-height: 350px;
		margin-left: var(--space-1);
		margin-right: var(--space-1);
	}

	.video-settled {
		max-width: 320px;
		max-height: 240px;
	}

	.video-caption {
		font-size: 1.8rem;
	}

	.video-hearts {
		font-size: 1.2rem;
		top: -15px;
		right: -15px;
	}

	/* Hide floating decorations on mobile to avoid clutter */
	.video-side-decorations {
		display: none;
	}

	.drawing-container-explosion {
		padding: var(--space-4);
	}

	.drawing-image-explosion {
		max-width: 250px;
		max-height: 200px;
	}

	.sparkle {
		font-size: 1.2rem;
	}

	/* Mobile moods section fixes - container */
	.moods-container {
		grid-template-columns: 1fr;
		gap: var(--space-4);
		padding: var(--space-1);
		justify-items: center;
	}

	.mood-card {
		max-width: 320px;
		width: 100%;
	}

	.mood-card img {
		object-position: center;
	}

	/* Mobile memory card fixes */
	.memory-card {
		padding: var(--space-4);
		margin-left: var(--space-1);
		margin-right: var(--space-1);
	}

	/* Mobile counter section fixes */
	.counter-section {
		padding: var(--space-4);
		margin-left: var(--space-1);
		margin-right: var(--space-1);
	}

	/* Mobile content container fixes */
	.content-container {
		padding: var(--space-2);
	}

	/* Mobile reasons section fixes */
	.reasons-section {
		padding: var(--space-2);
		margin-left: var(--space-1);
		margin-right: var(--space-1);
	}

	/* Mobile moods section fixes */
	.moods-section {
		padding: var(--space-2);
		margin-left: var(--space-1);
		margin-right: var(--space-1);
	}

	/* Hide desktop text on mobile */
	.desktop-only {
		display: none;
	}

	.mobile-only {
		display: block;
	}

	/* Mobile night mode adjustments */
	.night-mode-toggle {
		width: 50px;
		height: 50px;
		font-size: 1.5rem;
		top: 15px;
		right: 15px;
	}

	.night-mode .star {
		font-size: 0.6rem;
	}

	.night-mode .firefly {
		font-size: 0.8rem;
	}

	.night-mode .moon {
		font-size: 2.5rem;
		top: 5%;
		right: 10%;
	}
}

/* Hide mobile text on desktop */
.mobile-only {
	display: none;
}

.desktop-only {
	display: block;
}

.fade-in {
	animation: fadeIn 1s ease-in forwards;
}

.slide-up {
	animation: slideUp 0.5s ease-out forwards;
}

.bounce {
	animation: bounce 1s ease infinite;
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(50px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes bounce {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-10px);
	}
}

/* Night Mode Styles */
.night-mode-toggle {
	position: fixed;
	top: 20px;
	right: 20px;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.9);
	border: 2px solid rgba(190, 24, 93, 0.3);
	border-radius: 50%;
	width: 60px;
	height: 60px;
	font-size: 1.8rem;
	cursor: pointer;
	backdrop-filter: blur(10px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
	transition: all 0.3s ease;
}

.night-mode-toggle:hover {
	background: rgba(255, 255, 255, 1);
	box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
	transform: scale(1.05);
}

/* Night Mode Theme */
.birthday-main.night-mode {
	background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
	color: #e8e8f0;
	transition: all 0.8s ease;
}

.night-mode .message-section,
.night-mode .final-note,
.night-mode .memory-card,
.night-mode .counter-section,
.night-mode .reasons-section,
.night-mode .moods-section,
.night-mode .scratch-section {
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(15px);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.night-mode h1,
.night-mode h2,
.night-mode h3 {
	color: #ffd700;
	text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.night-mode p,
.night-mode .reason-text {
	color: #e8e8f0;
	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.night-mode .reason-card-large {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
	border: 1px solid rgba(255, 255, 255, 0.1);
	color: #e8e8f0;
}

.night-mode .mood-card {
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Fix white backgrounds in night mode */
.night-mode .joking-message {
	background: rgba(15, 15, 35, 0.9);
	border: 2px solid rgba(255, 215, 0, 0.3);
	color: #e8e8f0;
}

.night-mode .joking-message h3 {
	color: #ffd700;
}

.night-mode .scratch-final-message {
	background: rgba(15, 15, 35, 0.9);
	border: 2px solid rgba(255, 215, 0, 0.3);
	color: #e8e8f0;
}

.night-mode .scratch-final-message p {
	color: #e8e8f0;
}

.night-mode .gallery-nav {
	background: rgba(15, 15, 35, 0.9);
	border: 2px solid rgba(255, 215, 0, 0.3);
	color: #ffd700;
}

.night-mode .gallery-nav:hover {
	background: rgba(15, 15, 35, 1);
	box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
}

/* Fix moods section footer in night mode */
.night-mode .moods-footer {
	background: rgba(15, 15, 35, 0.9);
	border: 2px solid rgba(255, 215, 0, 0.3);
	border-radius: var(--radius-lg);
	padding: var(--space-4);
	margin-top: var(--space-4);
}

.night-mode .moods-footer h3 {
	color: #ffd700;
}

/* Fix drawing/surprise image sections in night mode */
.night-mode .drawing-container-settled,
.night-mode .drawing-container-explosion {
	background: rgba(15, 15, 35, 0.9);
	border: 2px solid rgba(255, 215, 0, 0.3);
	backdrop-filter: blur(15px);
}

.night-mode .drawing-message {
	background: rgba(15, 15, 35, 0.9);
	border: 2px solid rgba(255, 215, 0, 0.3);
	color: #e8e8f0;
}

.night-mode .drawing-message h3 {
	color: #ffd700;
}

.night-mode .drawing-message p {
	color: #e8e8f0;
}

/* Night mode video explosion overlay */
.night-mode .video-explosion-overlay {
	background: linear-gradient(135deg, rgba(15, 15, 35, 0.95) 0%, rgba(26, 26, 46, 0.95) 50%, rgba(22, 33, 62, 0.95) 100%);
}

.night-mode .explosion-content {
	background: rgba(15, 15, 35, 0.8);
	border: 2px solid rgba(255, 215, 0, 0.3);
	border-radius: var(--radius-xl);
	backdrop-filter: blur(20px);
}

.night-mode .video-container-explosion {
	background: rgba(15, 15, 35, 0.9);
	border: 2px solid rgba(255, 215, 0, 0.4);
	box-shadow: 0 25px 50px rgba(255, 215, 0, 0.1), 0 0 0 1px rgba(255, 215, 0, 0.2);
}

.night-mode .video-overlay-text {
	color: #ffd700;
	text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.night-mode .video-escape-hint {
	color: #e8e8f0;
	background: rgba(15, 15, 35, 0.8);
	border: 1px solid rgba(255, 215, 0, 0.3);
	border-radius: var(--radius-lg);
	padding: var(--space-2) var(--space-3);
	backdrop-filter: blur(10px);
}

/* Night mode sparkles in explosion */
.night-mode .sparkle {
	filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.8));
}

/* Night mode floating decorations */
.night-mode .floating-heart {
	filter: drop-shadow(0 0 3px rgba(255, 182, 193, 0.6));
}

.night-mode .floating-sparkle {
	filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.7));
}

/* Night mode video stars */
.video-night-stars {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: -1;
}

.video-star {
	position: absolute;
	pointer-events: none;
}

/* Night Stars */
.night-stars {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1;
}

.star {
	position: absolute;
	font-size: 0.8rem;
	color: #ffd700;
	filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.6));
}

/* Beautiful Moon */
.moon {
	position: absolute;
	top: 8%;
	right: 15%;
	font-size: 4rem;
	color: #ffd700;
	filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
	z-index: 1;
}

/* Fireflies */
.fireflies {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 2;
}

.firefly {
	position: absolute;
	font-size: 1rem;
	color: #90ee90;
	filter: drop-shadow(0 0 8px rgba(144, 238, 144, 0.8));
}

/* Night mode toggle button in night mode */
.night-mode .night-mode-toggle {
	background: rgba(15, 15, 35, 0.9);
	border: 2px solid rgba(255, 215, 0, 0.3);
	color: #ffd700;
}

.night-mode .night-mode-toggle:hover {
	background: rgba(15, 15, 35, 1);
	box-shadow: 0 12px 35px rgba(255, 215, 0, 0.2);
}
